<?php

namespace Webkul\DtTheme\Http\Controllers;

use Webkul\Product\Repositories\ProductRepository;
use Webkul\Shop\Http\Controllers\Controller;

class SaleController extends Controller
{
    /**
     * Display products that are on sale (special price set).
     */
    public function index()
    {
        // Fetch products that have a special_price
        $products = app(ProductRepository::class)->getAll()
            ->whereNotNull('special_price');

        return view('shop::products.index', compact('products'));
    }
}
